import { PigeonGang } from "../enums/PigeonGang";
import { PigeonRarity } from "../enums/PigeonRarity";
import { BasePigeon } from "../value-objects/BasePigeon";
import { PigeonPicture } from "../value-objects/PigeonPicture";
import { Stats } from "../value-objects/Stats";
import { Item } from "./Item";

export type PigeonData = Pick<
    Pigeon,
    | "id"
    | "ownerId"
    | "rarity"
    | "baseStats"
    | "basePigeonId"
    | "currentFatigue"
    | "maxFatigue"
    | "originalPicture"
    | "items"
    | "capturedAt"
    | "level"
    | "trickeryPoints"
    | "auraPoints"
    | "gang"
    | "captureId"
>;

export class Pigeon {
    id: string;
    ownerId: string;
    rarity: PigeonRarity;
    baseStats: Stats;
    basePigeonId: string;
    currentFatigue: number;
    maxFatigue: number;
    originalPicture: PigeonPicture;
    items: {
        hat: Item | null;
        mount: Item | null;
        leftWing: Item | null;
        rightWing: Item | null;
    };
    capturedAt: Date;
    level: number; // max lvl 5
    trickeryPoints: number;
    auraPoints: number;
    gang: PigeonGang;
    captureId: string;

    constructor(data: PigeonData) {
        this.id = data.id;
        this.ownerId = data.ownerId;
        this.rarity = data.rarity;
        this.baseStats = data.baseStats;
        this.basePigeonId = data.basePigeonId;
        this.currentFatigue = data.currentFatigue;
        this.maxFatigue = data.maxFatigue;
        this.originalPicture = data.originalPicture;
        this.items = data.items;
        this.capturedAt = data.capturedAt;
        this.level = data.level;
        this.trickeryPoints = data.trickeryPoints;
        this.auraPoints = data.auraPoints;
        this.gang = data.gang;
        this.captureId = data.captureId;
    }

    get totalStats(): Stats {
        const totalHealth =
            this.baseStats.health +
            (this.items.hat?.stats?.health ?? 0) +
            (this.items.mount?.stats.health ?? 0) +
            (this.items.leftWing?.stats.health ?? 0) +
            (this.items.rightWing?.stats.health ?? 0);
        const totalMuscularMass =
            this.baseStats.muscularMass +
            (this.items.hat?.stats?.muscularMass ?? 0) +
            (this.items.mount?.stats.muscularMass ?? 0) +
            (this.items.leftWing?.stats.muscularMass ?? 0) +
            (this.items.rightWing?.stats.muscularMass ?? 0);
        const totalAgility =
            this.baseStats.agility +
            (this.items.hat?.stats?.agility ?? 0) +
            (this.items.mount?.stats.agility ?? 0) +
            (this.items.leftWing?.stats.agility ?? 0) +
            (this.items.rightWing?.stats.agility ?? 0);
        return {
            health: totalHealth,
            muscularMass: totalMuscularMass,
            agility: totalAgility,
        };
    }

    toDocument(): PigeonDocument {
        return {
            id: this.id,
            ownerId: this.ownerId,
            rarity: this.rarity,
            baseStats: this.baseStats,
            basePigeonId: this.basePigeonId,
            currentFatigue: this.currentFatigue,
            maxFatigue: this.maxFatigue,
            originalPicture: this.originalPicture,
            items: this.items,
            capturedAt: this.capturedAt,
            level: this.level,
            trickeryPoints: this.trickeryPoints,
            auraPoints: this.auraPoints,
            gang: this.gang,
            captureId: this.captureId,
        };
    }
}

export const DEFAULT_MAX_FATIGUE = 5;

export type PigeonDocument = {
    id: string;
    ownerId: string;
    rarity: PigeonRarity;
    baseStats: Stats;
    basePigeonId: string;
    currentFatigue: number;
    maxFatigue: number;
    originalPicture: PigeonPicture;
    items: {
        hat: Item | null;
        mount: Item | null;
        leftWing: Item | null;
        rightWing: Item | null;
    };
    capturedAt: Date;
    level: number;
    trickeryPoints: number;
    auraPoints: number;
    gang: PigeonGang;
    captureId: string;
};

export type PigeonWithBasePigeon = PigeonDocument & {
    basePigeon: BasePigeon;
};
