/* eslint-disable @typescript-eslint/no-unused-vars */
import { Pigeon } from "../domain/entities/Pigeon";
import { defaultTrainer, Trainer, TrainerDocument } from "../domain/entities/Trainer";
import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { BirdType } from "../domain/enums/BirdType";
import { PigeonDistinctiveness } from "../domain/enums/PigeonDistinctiveness";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { AnalysisService, RankedBasePigeon } from "../domain/services/AnalysisService";
import { Coordinates, GeoLocationService } from "../domain/services/GeoLocationService";
import { CapturePigeonUseCase } from "../use-cases/CapturePigeonUseCase";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";

class FakeTrainerRepository implements TrainerRepository {
    private trainers: { [id: string]: Trainer } = {};

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers[id] || null;
    }

    async update(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer({ ...trainer, decks: [] });
    }

    add(trainer: Trainer) {
        this.trainers[trainer.id] = trainer;
    }
}

class FakePigeonRepository implements PigeonRepository {
    public pigeons: { [id: string]: Pigeon } = {};

    async save(pigeon: Pigeon): Promise<void> {
        this.pigeons[pigeon.id] = pigeon;
    }

    async getByOwnerId(ownerId: string, limit?: number, offset?: number): Promise<Pigeon[]> {
        let pigeons = Object.values(this.pigeons).filter((pigeon) => pigeon.ownerId === ownerId);

        // Sort by capturedAt desc to match the real implementation
        pigeons = pigeons.sort((a, b) => b.capturedAt.getTime() - a.capturedAt.getTime());

        if (offset && offset > 0) {
            pigeons = pigeons.slice(offset);
        }

        if (limit && limit > 0) {
            pigeons = pigeons.slice(0, limit);
        }

        return pigeons;
    }

    async getByCaptureId(captureId: string): Promise<Pigeon | null> {
        const pigeon = Object.values(this.pigeons).find((p) => p.captureId === captureId);
        return pigeon || null;
    }
}

class FakeAnalysisService implements AnalysisService {
    analyzePigeonPicture(storageFilePath: string): Promise<RankedBasePigeon[]> {
        // Return a mock ranked base pigeon for testing
        const mockBasePigeon = {
            id: "mock-base-pigeon-id",
            name: { fr: "Pigeon Test", en: "Test Pigeon" },
            slug: "test_pigeon",
            story: { fr: "Un pigeon de test", en: "A test pigeon" },
            skin: {
                smallUrl: "https://example.com/test_thumb.jpg",
                originalUrl: "https://example.com/test.jpg",
            },
            typicalAnalysis: {
                isFake: 0,
                isBird: true,
                isDead: false,
                isABabyPigeon: false,
                birdType: BirdType.PIGEON,
                distinctiveness: PigeonDistinctiveness.COMMON,
                description: "A test pigeon for unit testing",
            },
        };

        return Promise.resolve([
            {
                basePigeon: mockBasePigeon,
                score: 85,
            },
        ]);
    }
}

class FakeGeoLocationService implements GeoLocationService {
    determineGang(coordinates: Coordinates, date?: Date): PigeonGang {
        // For testing, use a deterministic approach based on coordinates
        const { latitude, longitude } = coordinates;

        // Use a simple rule for testing
        if (latitude > 0 && longitude > 0) {
            return PigeonGang.ONE;
        } else if (latitude > 0 && longitude <= 0) {
            return PigeonGang.TWO;
        } else if (latitude <= 0 && longitude <= 0) {
            return PigeonGang.THREE;
        } else if (latitude <= 0 && longitude > 0) {
            return PigeonGang.FOUR;
        }

        // Default
        return PigeonGang.ONE;
    }
}

class FakeAnalysisJobRepository implements AnalysisJobRepository {
    private jobs: Map<string, AnalysisJob> = new Map();

    async create(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async update(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async getByCaptureId(captureId: string): Promise<AnalysisJob | null> {
        return this.jobs.get(captureId) || null;
    }

    async getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]> {
        return Array.from(this.jobs.values()).filter((job) => job.trainerId === trainerId);
    }

    // Helper method for testing
    addJob(job: AnalysisJob): void {
        this.jobs.set(job.captureId, job);
    }

    clear(): void {
        this.jobs.clear();
    }
}

describe("CapturePigeonWithAnalysisUseCase", () => {
    const fakeTrainerRepo = new FakeTrainerRepository();
    const fakePigeonRepo = new FakePigeonRepository();
    const fakeAnalysisJobRepo = new FakeAnalysisJobRepository();
    const analysisService = new FakeAnalysisService();
    const geoLocationService = new FakeGeoLocationService();
    const capturePigeonUseCase = new CapturePigeonUseCase(
        fakeTrainerRepo,
        fakePigeonRepo,
        analysisService,
        geoLocationService,
        fakeAnalysisJobRepo,
    );

    it("should return early if no analysis job found", async () => {
        const filePath = "shots/unknownTrainerId/unknownCaptureId/40.7128_-74.0060_file.jpg";
        // Should not throw, just return early since no analysis job exists
        await capturePigeonUseCase.execute(filePath, filePath);
        // No pigeon should be created
        expect(Object.keys(fakePigeonRepo.pigeons)).toHaveLength(0);
    });

    it("should mark job as error if no pigeon balls", async () => {
        const trainerId = "noBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 0 });
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeonId: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        // Check that the job was marked as error
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.ERROR);
        expect(updatedJob?.errorMessage).toBe("No capture stock available");
        expect(updatedJob?.errorCode).toBe(AnalysisJobErrorCode.NO_CAPTURE_STOCK);
    });

    it("should mark job as error with the right error code if analysis failed", async () => {
        const trainerId = "oneBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeonId: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        // Mock analysis service to throw an error
        const originalAnalyze = analysisService.analyzePigeonPicture;
        analysisService.analyzePigeonPicture = jest.fn().mockRejectedValue(new Error("Analysis failed"));

        await capturePigeonUseCase.execute(filePath, filePath);

        // Restore original method
        analysisService.analyzePigeonPicture = originalAnalyze;

        // Check that the job was marked as error
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.ERROR);
        expect(updatedJob?.errorCode).toBe(AnalysisJobErrorCode.ANALYSIS_FAILED);
    });

    it("should capture a pigeon with invalid coordinates", async () => {
        const trainerId = "oneBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/${captureId}/invalid_coords_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeonId: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.THREE); // Default coordinates (0,0) maps to Gang THREE in our test logic
        expect(capturedPigeon?.captureId).toBe(captureId);

        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);

        // Check that the job was marked as finished
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.FINISHED);
        expect(updatedJob?.pigeonId).toBe(capturedPigeon?.id);
    });

    it("should capture a pigeon with valid coordinates", async () => {
        const trainerId = "twoBallTrainerId";
        const captureId = "testCaptureId2";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        // Coordinates in the filename (positive lat, negative long - should be Gang TWO in our test)
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeonId: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.TWO); // Based on our test coordinates
        expect(capturedPigeon?.captureId).toBe(captureId);

        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);

        // Check that the job was marked as finished
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.FINISHED);
        expect(updatedJob?.pigeonId).toBe(capturedPigeon?.id);
    });
});
