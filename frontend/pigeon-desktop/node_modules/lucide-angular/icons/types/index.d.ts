declare type HtmlAttributes = {
    [key: string]: string | number;
};
export declare type LucideIconNode = readonly [string, HtmlAttributes];
export declare type LucideIconData = readonly LucideIconNode[];
export declare type LucideIcons = {
    [key: string]: LucideIconData;
};
/** @deprecated Use LucideIconData instead. Will be removed in v1.0. */
export declare type IconData = LucideIconData;
/** @deprecated Use LucideIconNode instead. Will be removed in v1.0. */
export declare type IconNode = LucideIconNode;
/** @deprecated Use LucideIcons instead. Will be removed in v1.0. */
export declare type Icons = LucideIcons;
export {};
